<template>
    <div class="device-selector-wrap">
        <div class="filter-area">
            <el-form :model="filterForm" class="filter-form">
                <!-- keyword 100% 宽度 -->
                <el-form-item class="full-width">
                    <el-input
                        v-model="filterForm.keyword"
                        :placeholder="$t('InputToSearch01')"
                        clearable
                        @input="KeywordSearch"
                    />
                </el-form-item>
                <!-- 新增：服务状态 + 服务日期 -->
                <div class="half-width-row">
                    <el-form-item class="half-width">
                        <el-select
                            v-model="filterForm.serviceStatus"
                            :placeholder="$t('service_status')"
                            clearable
                            @change="handleSearch"
                            class="w-full"
                        >
                            <el-option
                                :label="$t('Un Configured')"
                                :value="0"
                            />
                            <el-option :label="$t('Normal')" :value="1" />
                            <el-option :label="$t('Expired')" :value="2" />
                            <el-option :label="$t('Near Expire')" :value="3" />
                        </el-select>
                    </el-form-item>

                    <el-form-item class="half-width">
                        <el-date-picker
                            v-model="filterForm.serviceExpireDate"
                            type="month"
                            :placeholder="$t('service_expire_time')"
                            value-format="YYYY-MM-DD"
                            clearable
                            @change="handleSearch"
                            class="w-full"
                        />
                    </el-form-item>
                </div>
                <!-- 其他筛选项 50% 宽度 -->
                <div class="half-width-row">
                    <el-form-item class="half-width">
                        <el-select
                            collapse-tags
                            v-model="filterForm.projectIds"
                            multiple
                            :placeholder="$t('Belonging project')"
                            clearable
                            @change="handleSearch"
                            class="w-full"
                        >
                            <el-option
                                v-for="item in projectOptions"
                                :key="item.id"
                                :label="item.projectName"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item class="half-width">
                        <el-tree-select
                            v-model="filterForm.cities"
                            :data="cityOptions"
                            :render-after-expand="true"
                            multiple
                            clearable
                            :placeholder="$t('Belonged City')"
                            class="w-full"
                            @change="handleSearch"
                        />
                    </el-form-item>
                </div>

                <div class="half-width-row">
                    <el-form-item class="half-width">
                        <el-select
                            v-model="filterForm.vehicleType"
                            :placeholder="$t('Vehicle type')"
                            clearable
                            multiple
                            @change="handleSearch"
                            class="w-full"
                        >
                            <el-option
                                v-for="item in vehicleTypeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item class="half-width">
                        <el-date-picker
                            v-model="filterForm.activeDate"
                            type="date"
                            :placeholder="$t('Activation time')"
                            value-format="YYYY-MM-DD"
                            clearable
                            @change="handleSearch"
                            class="w-full"
                        />
                    </el-form-item>
                </div>
            </el-form>
        </div>
        <div class="checkbox-area">
            <div class="checkbox-header">
                <el-checkbox
                    v-model="checkAll"
                    :indeterminate="isIndeterminate"
                    @change="handleCheckAllChange"
                    style="width: auto"
                >
                    {{ $t('Select all') }}
                </el-checkbox>
                <div class="selected-info">
                    {{ $t('Selected') }} {{ selectedDeviceIds.length }}
                    {{ $t('common_tiao') }}
                </div>
            </div>

            <div class="checkbox-list" v-loading="loading">
                <el-checkbox-group
                    :model-value="selectedDeviceIds"
                    @update:model-value="handleSelectionChange"
                    class="device-checkbox-group column-selector"
                >
                    <div
                        v-for="item in tableData"
                        :key="item.id"
                        class="device-checkbox-item"
                    >
                        <el-checkbox
                            :value="item.id"
                            class="device-checkbox pl-4 relative"
                        >
                            <div
                                class="absolute w-2.5 h-2.5 border-l border-b left-0 rounded-bl-sm opacity-60"
                            ></div>

                            <div class="device-info">
                                <div class="device-sn">{{ item.sn }}</div>
                            </div>
                        </el-checkbox>
                    </div>
                </el-checkbox-group>

                <div
                    v-if="!loading && tableData.length === 0"
                    class="empty-data"
                >
                    {{ $t('暂无数据') }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {
    ref,
    watch,
    defineProps,
    defineEmits,
    onMounted,
    reactive,
    computed,
} from 'vue'
import { useStore } from 'vuex'
import powerApi from '@/apiService/power'
import { useI18n } from 'vue-i18n'
import { debounce } from 'lodash-es'

const { t } = useI18n()
const store = useStore()
const isDark = computed(() => {
    return store.state.theme.isDark
})
const props = defineProps({
    // 已选择的设备ID
    selectedDeviceIds: {
        type: Array,
        default: () => [],
    },
})

const emit = defineEmits(['update:selectedDeviceIds'])

// 暴露重置方法给父组件
const resetFilters = () => {
    // 重置筛选条件
    filterForm.keyword = ''
    filterForm.projectIds = ''
    filterForm.vehicleType = ''
    filterForm.cities = ''
    filterForm.activeDate = ''
    filterForm.serviceStatus = ''
    filterForm.serviceExpireDate = ''

    // 重置分页
    pagination.current = 1

    // 重新获取数据
    getDeviceList()
}

// 暴露方法给父组件
defineExpose({
    resetFilters,
})

// 内部状态
const tableData = ref([])
const loading = ref(false)
const pagination = reactive({
    current: 1,
    size: 5000,
    total: 0,
})
const filterForm = reactive({
    keyword: '',
    projectIds: '',
    vehicleType: '',
    cities: '',
    activeDate: '',
    serviceStatus: '',
    serviceExpireDate: '',
})

const projectOptions = ref([])
const vehicleTypeOptions = ref([])
const cityOptions = ref([])
const checkAll = ref(false)
const isIndeterminate = ref(false)

// 获取设备列表
const getDeviceList = async () => {
    loading.value = true
    try {
        const params = {
            current: pagination.current,
            size: pagination.size,
            keyword: filterForm.keyword || undefined,
            projectIds: filterForm.projectIds || undefined,
            vehicleType: filterForm.vehicleType || undefined,
            cities: filterForm.cities || undefined,
            activeDate: filterForm.activeDate || undefined,
            serviceStatus: filterForm.serviceStatus || undefined,
            serviceExpireDate: filterForm.serviceExpireDate || undefined,
            // serviceStatus: 0,
        }

        // const res = await powerApi.getDevicePageList(params)
        const res = await powerApi.getSimplePageList(params)
        if (res.data.code === 0) {
            tableData.value = res.data.data.records || []
            pagination.total = res.data.data.total || 0
        }
    } catch (error) {
        console.error('获取设备列表失败:', error)
    } finally {
        loading.value = false
    }
}

// 获取筛选选项数据
const getFilterOptions = async () => {
    try {
        // 获取项目列表
        const projectRes = await powerApi.getProjectPageList({
            current: 1,
            size: 1000,
        })
        if (projectRes.data.code === 0) {
            projectOptions.value = projectRes.data.data.records || []
        }

        // 获取车辆类型选项
        vehicleTypeOptions.value = [
            { value: 'car', label: t('Car') },
            { value: 'truck', label: t('Truck') },
            { value: 'bus', label: t('Bus') },
        ]

        // 获取城市选项
        cityOptions.value = []
    } catch (error) {
        console.error('获取筛选选项失败:', error)
    }
}

// 搜索处理
const KeywordSearch = debounce(() => {
    pagination.current = 1
    getDeviceList()
}, 300)

const handleSearch = () => {
    pagination.current = 1
    getDeviceList()
}

// 选择处理
const handleSelectionChange = (value) => {
    emit('update:selectedDeviceIds', value)
}

const handleCheckAllChange = (val) => {
    if (val) {
        const allIds = tableData.value.map((item) => item.id)
        emit('update:selectedDeviceIds', allIds)
    } else {
        emit('update:selectedDeviceIds', [])
    }
}

// 更新全选状态
const updateCheckAllStatus = () => {
    const selectedCount = props.selectedDeviceIds.length
    const totalCount = tableData.value.length

    checkAll.value = selectedCount === totalCount && totalCount > 0
    isIndeterminate.value = selectedCount > 0 && selectedCount < totalCount
}

// 初始化
onMounted(() => {
    getFilterOptions()
    getDeviceList()
})

// 监听选择变化
watch(() => props.selectedDeviceIds, updateCheckAllStatus, { immediate: true })

// 监听内部变化
watch(tableData, () => {
    updateCheckAllStatus()
})
</script>

<style scoped lang="less">
.device-selector-wrap {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.filter-area {
    padding-bottom: 4px;

    .filter-form {
        .full-width {
            width: 100%;
            margin-bottom: 8px;
        }

        .half-width-row {
            display: flex;
            gap: 12px;
            margin-bottom: 8px;

            .half-width {
                flex: 1;
                width: 50%;
            }
            :deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
                width: 100%;
            }
        }
    }

    :deep(.el-form-item) {
        margin-bottom: 0;
    }

    :deep(.el-select),
    :deep(.el-cascader),
    :deep(.el-date-picker) {
        width: 100%;
    }
}

.checkbox-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 0;

    .checkbox-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        border-bottom: 1px solid #ebeef5;
        padding-bottom: 8px;
        .selected-info {
            color: var(--text-80);
            font-size: 14px;
        }
    }

    .checkbox-list {
        flex: 1;
        overflow-y: auto;
        padding-top: 12px;
        .device-checkbox-group {
            display: flex;
            flex-direction: column;
        }

        .device-checkbox-item {
            transition: all 0.3s;

            &.disabled {
                opacity: 0.6;
            }

            .device-checkbox {
                width: 100%;
                height: 30px;
                :deep(.el-checkbox__label) {
                    width: 100%;
                    padding-left: 8px;
                }
            }

            .device-info {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .device-sn {
                    font-weight: 500;
                    font-size: 14px;
                    color: var(--text-80);
                }

                .device-project {
                    font-size: 12px;
                    color: var(--text-60);
                }

                .bound-tag {
                    font-size: 12px;
                    color: #f56c6c;
                    background-color: #fef0f0;
                    padding: 2px 6px;
                    border-radius: 4px;
                    display: inline-block;
                    width: fit-content;
                }
            }
        }

        .empty-data {
            text-align: center;
            color: #909399;
            font-size: 14px;
            padding: 40px 0;
        }
    }
}

.pagination-area {
    display: flex;
    justify-content: flex-end;
    padding-top: 16px;
}
</style>
